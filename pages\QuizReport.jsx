import React, { useState, useEffect, use } from 'react';
import { FiCheckCircle, FiChevronDown, FiShare2, FiRefreshCw } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';

const QuizReport = () => {
  // Local storage
  const reportData = JSON.parse(localStorage.getItem('mcqReportData'));
  const durationInSeconds = parseInt(localStorage.getItem('mcq_quiz_duration_for_result')) || 1800;
  const navigate = useNavigate();

  // Extract data from reportData
  const {
    topicName = 'Linked Lists: Singly, Double and Circular',
    questions: quizQuestions = [],
    noOfAttempts = 0
  } = reportData || {};

  // State for AI feedback
  const [aiFeedback, setAiFeedback] = useState({
    strengths: '',
    areasToImprove: '',
    recommendations: []
  });
  const [loadingFeedback, setLoadingFeedback] = useState(true);
  const [username, setusername] = useState("Name");

  useEffect(() => {
    const Userdata = JSON.parse(localStorage.getItem('user-data'));
    setusername(Userdata?.firstName);
    console.log(username)
  }, [username]);

  useEffect(() => {
    const fetchFeedback = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/v1/ideoData/getQuizFeedback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            quizTopic: topicName,
            quizAccuracy: `${accuracy}%`
          })
        });

        const data = await response.json();

        if (data.success) {
          // Parse the feedback text
          const feedbackText = data.data;

          // Extract strengths
          const strengthsMatch = feedbackText.match(/Strengths:([\s\S]*?)Areas to Improve:/i) ||
            feedbackText.match(/Well done([\s\S]*?)However,/i);
          const strengths = strengthsMatch ? strengthsMatch[1].trim() :
            "You've shown good understanding in some areas of this topic.";

          // Extract areas to improve
          const areasMatch = feedbackText.match(/Areas to Improve:([\s\S]*?)Suggestions:/i) ||
            feedbackText.match(/However,([\s\S]*?)Suggestions:/i);
          const areasToImprove = areasMatch ? areasMatch[1].trim() :
            "There are some areas where you can focus to improve your understanding.";

          // Extract recommendations
          const recMatch = feedbackText.match(/Suggestions[^:]*:([\s\S]*)/i);
          let recommendations = [];
          if (recMatch) {
            recommendations = recMatch[1].split('\n')
              .filter(line => line.trim().match(/^-|•|\d\./))
              .map(line => line.replace(/^-|•|\d\./, '').trim())
              .filter(line => line.length > 0);
          }

          if (recommendations.length === 0) {
            recommendations = [
              "Review the questions you got wrong and understand the correct solutions",
              "Practice more problems on this topic to reinforce your understanding",
              "Seek clarification on concepts you find challenging"
            ];
          }

          setAiFeedback({
            strengths,
            areasToImprove,
            recommendations
          });
        }
      } catch (error) {
        console.error('Error fetching feedback:', error);
        setAiFeedback({
          strengths: "You've shown good understanding in some areas of this topic.",
          areasToImprove: "There are some areas where you can focus to improve your understanding.",
          recommendations: [
            "Review the questions you got wrong and understand the correct solutions",
            "Practice more problems on this topic to reinforce your understanding",
            "Seek clarification on concepts you find challenging"
          ]
        });
      } finally {
        setLoadingFeedback(false);
      }
    };

    fetchFeedback();
  }, [topicName]);

  const formatDuration = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours > 0 ? `${hours}h ` : ''}${minutes}m`;
  };

  // Map questions data
  const questions = quizQuestions.map(q => ({
    qNo: q.qNo,
    question: q.question,
    correctAnswer: q.correctOption,
    yourAnswer: q.markedOption || 'Not Attempted',
    score: q.markedOption === q.correctOption ? 1 : 0
  }));

  const totalQuestions = quizQuestions.length;
  const correctAnswers = questions.filter(q => q.score === 1).length;
  const accuracy = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
  const status = accuracy >= 40 ? 'Passed' : 'Failed';
  const bestScore = `${correctAnswers}/${totalQuestions}`;

  const handleShareScore = () => {
    window.print();
  };

  const handleRetakeQuiz = () => {
    navigate('/mcq-quiz');
  };

  return (
    <div className="min-h-screen bg-slate-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl bg-white shadow-lg rounded-lg">
        {/* Header */}
        <div
          className="text-white p-4 rounded-t-lg"
          style={{ background: 'linear-gradient(90deg, #A6C8FF 0%, #CABDFF 100%)' }}
        >
          <h1 className="text-lg font-semibold">Quiz Report</h1>
        </div>

        <div className="p-6 md:p-8 space-y-8">
          {/* Topic Name, Student Info & Stats */}
          <div className="bg-white p-6 rounded-lg shadow-md text-black">
            <h2 className="text-2xl font-bold text-blue-800 mb-1">
              {topicName}
            </h2>

            <div className="flex flex-col md:flex-row justify-between items-start mt-3">
              {/* Left Side: Student Name, Date, Duration */}
              <div className="mb-4 md:mb-0">
                <h3 className="text-xl font-semibold text-gray-700">{username}</h3>
                <p className="text-sm text-gray-500 mt-8">
                  Date: {new Date().toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' })}
                </p>
                <p className="text-sm text-gray-500">
                  Duration: {formatDuration(durationInSeconds)}
                </p>
              </div>

              {/* Right Side: Stats */}
              <div className="grid grid-cols-2 gap-3 w-full md:w-auto md:min-w-[280px] md:max-w-xs text-left">
                <div className="bg-gray-50 p-3 rounded-lg shadow-sm">
                  <p className="text-xs text-gray-500">Total Questions</p>
                  <p className="text-lg font-bold text-gray-800">{totalQuestions}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg shadow-sm">
                  <p className="text-xs text-gray-500">Correct Answers</p>
                  <p className="text-lg font-bold text-green-600">{correctAnswers}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg shadow-sm">
                  <p className="text-xs text-gray-500">Accuracy</p>
                  <p className="text-lg font-bold text-gray-800">{accuracy}%</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg shadow-sm">
                  <p className="text-xs text-gray-500">Status</p>
                  <div className="flex items-center">
                    <p className="text-lg font-semibold text-black-600">{status}</p>
                    {status === 'Passed' && <FiCheckCircle className="ml-1 text-green-800 h-5 w-5" />}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Question Breakdown */}
          <div className="bg-white p-6 rounded-lg shadow-md text-black">
            <div className="border-b border-gray-200 pb-4 flex flex-col sm:flex-row justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-gray-800 mb-3 sm:mb-0">Question Breakdown</h3>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full text-sm text-left text-gray-700 border border-gray-300/80">
                <thead className="text-xs text-gray-700 uppercase bg-gray-100 border-b border-gray-300/80">
                  <tr>
                    <th scope="col" className="px-4 py-3">Q.No</th>
                    <th scope="col" className="px-6 py-3">Question</th>
                    <th scope="col" className="px-6 py-3">Correct Answer</th>
                    <th scope="col" className="px-6 py-3">Your Answer</th>
                    <th scope="col" className="px-4 py-3 text-center">Score</th>
                  </tr>
                </thead>
                <tbody>
                  {questions.map((q) => (
                    <tr key={q.qNo} className="bg-white border-b border-gray-300/80 hover:bg-gray-50">
                      <td className="px-4 py-4 font-medium text-gray-900">{q.qNo}</td>
                      <td className="px-6 py-4">{q.question}</td>
                      <td className="px-6 py-4 font-medium">{q.correctAnswer}</td>
                      <td className={`px-6 py-4 font-medium ${q.yourAnswer === q.correctAnswer ? '' : ''
                        }`}>
                        {q.yourAnswer}
                      </td>
                      <td className={`px-4 py-4 text-center font-bold ${q.score === 1 ? 'text-green-600' : 'text-red-600'
                        }`}>
                        {q.score}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Personalized Feedback */}
          <div className="bg-white p-6 rounded-lg shadow-md text-black">
            <div className="flex items-center mb-4">
              <div className="bg-white p-2 rounded-full mr-3">
                <img
                  src="/Margin.svg"
                  alt="AI Mentor Icon"
                  className="h-10 w-10 text-purple-700"
                />
              </div>
              <h4 className="text-xl font-semibold">Personalized Feedback from AI Mentor</h4>
            </div>

            {loadingFeedback ? (
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              </div>
            ) : (
              <>
                <p className="text-sm mb-3">
                  <span className="font-semibold">Strengths:</span> {aiFeedback.strengths}
                </p>
                <p className="text-sm mb-4">
                  <span className="font-semibold">Areas to Improve:</span> {aiFeedback.areasToImprove}
                </p>
                <div>
                  <h5 className="font-semibold mb-2">Recommendations:</h5>
                  <ul className="list-disc list-inside text-sm space-y-1">
                    {aiFeedback.recommendations.map((rec, index) => (
                      <li key={index}>{rec}</li>
                    ))}
                  </ul>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Footer Actions */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg flex flex-col sm:flex-row justify-between items-center">
          <div className="text-sm text-gray-600 mb-4 sm:mb-0">
            Attempts: <span className="font-semibold">{noOfAttempts}</span> |
            Best Score: <span className="font-semibold text-gray-600">{bestScore}</span>
          </div>
          <div className="flex items-center space-x-3">
            <button
              className="text-sm text-black-600 hover:text-black-800 font-medium py-2 px-4 rounded-md border border-gray-300 hover:bg-blue-50 flex items-center"
              onClick={handleRetakeQuiz}
            >
              <FiRefreshCw className="mr-2 h-4 w-4" /> Retake Quiz
            </button>
            <button
              className="text-sm text-[#7C3AED] bg-white hover:bg-purple-400 border border-[#7C3AED] font-medium py-2 px-4 rounded-md flex items-center"
              onClick={handleShareScore}
            >
              <FiShare2 className="mr-2 h-4 w-4" /> Share Score
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizReport;